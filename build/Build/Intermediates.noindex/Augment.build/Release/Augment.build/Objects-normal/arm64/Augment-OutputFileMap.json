{"": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-master.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-master.swiftdeps"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/AugmentApp.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentApp.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentApp.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentApp.o"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/ConflictResolutionView.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/ConflictResolutionView.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/ConflictResolutionView.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/ConflictResolutionView.o"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/ContentView.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/ContentView.o"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/SearchView.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/SearchView.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/SearchView.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/SearchView.o"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/SpaceDetailView.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/SpaceDetailView.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/SpaceDetailView.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/SpaceDetailView.o"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/VersionBrowser.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/VersionBrowser.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/VersionBrowser.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/VersionBrowser.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/AugmentSpace.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentSpace.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentSpace.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentSpace.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/BackupManager.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/BackupManager.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/BackupManager.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/BackupManager.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/ConflictResolution.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/ConflictResolution.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/ConflictResolution.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/ConflictResolution.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/FileItem.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/FileItem.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/FileItem.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/FileItem.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/FileSystemMonitor.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/FileSystemMonitor.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/FileSystemMonitor.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/FileSystemMonitor.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/FileType.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/FileType.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/FileType.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/FileType.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/NetworkSync.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/NetworkSync.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/NetworkSync.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/NetworkSync.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/PreviewEngine.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/PreviewEngine.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/PreviewEngine.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/PreviewEngine.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/SearchEngine.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/SearchEngine.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/SearchEngine.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/SearchEngine.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/SnapshotManager.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/SnapshotManager.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/SnapshotManager.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/SnapshotManager.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/VersionControl.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/VersionControl.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/VersionControl.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/VersionControl.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentFileSystem/AugmentFUSE.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentFUSE.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentFUSE.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentFUSE.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentFileSystem/AugmentFileSystem.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentFileSystem.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentFileSystem.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/AugmentFileSystem.o"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentFileSystem/FileOperationInterceptor.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/FileOperationInterceptor.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o"}, "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/DerivedSources/GeneratedAssetSymbols.swift": {"index-unit-output-path": "/Augment.build/Release/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o"}}