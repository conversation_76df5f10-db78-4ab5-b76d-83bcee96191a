"/Users/<USER>/Desktop/space/AugmentApp/Augment/ContentView.swift":
  const-values: "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-master.swiftconstvalues"
  abi-baseline-json: "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment.abi.json"
  objc-header: "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-Swift.h"
  swiftdoc: "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment.swiftdoc"
  swiftsourceinfo: "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo"
  diagnostics: "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-master.dia"
  swiftmodule: "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment.swiftmodule"
  dependencies: "/Users/<USER>/Desktop/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Release/Augment.build/Objects-normal/arm64/Augment-master.d"
