{"source": "/Users/<USER>/Desktop/space/AugmentApp/Augment/VersionBrowser.swift", "tables": {"Localizable": [{"comment": "", "key": "Version History: %@", "location": {"startingColumn": 22, "startingLine": 26}}, {"comment": "", "key": "Compare", "location": {"startingColumn": 24, "startingLine": 31}}, {"comment": "", "key": "Compare", "location": {"startingColumn": 28, "startingLine": 43}}, {"comment": "", "key": "Rest<PERSON>", "location": {"startingColumn": 28, "startingLine": 49}}, {"comment": "", "key": "No Version History", "location": {"startingColumn": 30, "startingLine": 75}}, {"comment": "", "key": "This file doesn't have any versions yet.", "location": {"startingColumn": 30, "startingLine": 79}}, {"comment": "", "key": "Create Version Now", "location": {"startingColumn": 32, "startingLine": 105}}, {"comment": "", "key": "To create versions:", "location": {"startingColumn": 34, "startingLine": 85}}, {"comment": "", "key": "• Right-click the file and select 'Create Version Now'", "location": {"startingColumn": 34, "startingLine": 89}}, {"comment": "", "key": "• Versions are automatically created when files are modified", "location": {"startingColumn": 34, "startingLine": 93}}, {"comment": "", "key": "• Make sure the file is in an Augment space", "location": {"startingColumn": 34, "startingLine": 97}}, {"comment": "", "key": "Versions (%lld)", "location": {"startingColumn": 34, "startingLine": 129}}, {"comment": "", "key": "No comment", "location": {"startingColumn": 58, "startingLine": 170}}, {"comment": "", "key": "Restore to This Version", "location": {"startingColumn": 48, "startingLine": 211}}, {"comment": "", "key": "Compare From This Version", "location": {"startingColumn": 52, "startingLine": 217}}, {"comment": "", "key": "Compare To This Version", "location": {"startingColumn": 52, "startingLine": 221}}, {"comment": "", "key": "Select a version to preview", "location": {"startingColumn": 30, "startingLine": 260}}, {"comment": "", "key": "%@", "location": {"startingColumn": 22, "startingLine": 545}}, {"comment": "", "key": "No differences", "location": {"startingColumn": 26, "startingLine": 649}}, {"comment": "", "key": "%lld", "location": {"startingColumn": 30, "startingLine": 683}}, {"comment": "", "key": "Show Overlay", "location": {"startingColumn": 24, "startingLine": 726}}, {"comment": "", "key": "Before", "location": {"startingColumn": 30, "startingLine": 754}}, {"comment": "", "key": "After", "location": {"startingColumn": 30, "startingLine": 765}}, {"comment": "", "key": "Binary File Comparison", "location": {"startingColumn": 18, "startingLine": 807}}, {"comment": "", "key": "The files are different, but a visual comparison is not available for this file type.", "location": {"startingColumn": 17, "startingLine": 812}}, {"comment": "", "key": "Original Version", "location": {"startingColumn": 26, "startingLine": 820}}, {"comment": "", "key": "Date: %@", "location": {"startingColumn": 26, "startingLine": 823}}, {"comment": "", "key": "Size: %@", "location": {"startingColumn": 26, "startingLine": 824}}, {"comment": "", "key": "New Version", "location": {"startingColumn": 26, "startingLine": 831}}, {"comment": "", "key": "Date: %@", "location": {"startingColumn": 26, "startingLine": 834}}, {"comment": "", "key": "Size: %@", "location": {"startingColumn": 26, "startingLine": 835}}, {"comment": "", "key": "Restore Version", "location": {"startingColumn": 18, "startingLine": 869}}, {"comment": "", "key": "Are you sure you want to restore this version?", "location": {"startingColumn": 18, "startingLine": 872}}, {"comment": "", "key": "Comment (optional)", "location": {"startingColumn": 23, "startingLine": 889}}, {"comment": "", "key": "Version Date: %@", "location": {"startingColumn": 22, "startingLine": 876}}, {"comment": "", "key": "Version Comment: %@", "location": {"startingColumn": 26, "startingLine": 880}}, {"comment": "", "key": "Cancel", "location": {"startingColumn": 24, "startingLine": 893}}, {"comment": "", "key": "Rest<PERSON>", "location": {"startingColumn": 24, "startingLine": 900}}]}, "version": 1}