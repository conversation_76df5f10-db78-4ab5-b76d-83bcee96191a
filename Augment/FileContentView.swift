import Foundation
import SwiftUI

struct FileContentView: View {
    let file: FileItem
    @State private var fileContent: String = ""
    @State private var image: NSImage? = nil
    @State private var isTextFile: Bool = true
    @State private var loadID = UUID()  // Used to force reload

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text(file.name)
                    .font(.headline)
                Spacer()
                Button(action: reload) {
                    Image(systemName: "arrow.clockwise")
                }
                .help("Reload from disk")
            }
            .padding(.bottom, 4)

            Divider()

            if isTextFile {
                ScrollView {
                    Text(fileContent)
                        .font(.system(.body, design: .monospaced))
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            } else if let image = image {
                ScrollView([.horizontal, .vertical]) {
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .padding()
                }
            } else {
                Text("Unable to display file content.")
                    .foregroundColor(.secondary)
                    .padding()
            }
        }
        .padding()
        .onAppear(perform: loadFileContent)
        .id(loadID)  // Forces view reload when loadID changes
    }

    private func loadFileContent() {
        let fileExtension = URL(fileURLWithPath: file.path).pathExtension.lowercased()
        isTextFile = [
            "txt", "md", "swift", "java", "c", "cpp", "h", "hpp", "py", "js", "html", "css", "xml",
            "json",
        ].contains(fileExtension)
        let url = URL(fileURLWithPath: file.path)

        // CRITICAL FIX: Clear any cached content first to ensure fresh load
        fileContent = ""
        image = nil

        // Add debug logging to track file content loading
        print("FileContentView: Loading content for \(url.lastPathComponent) (loadID: \(loadID))")

        if isTextFile {
            // Use FileManager to ensure we get the latest file content
            if FileManager.default.fileExists(atPath: file.path),
                let data = try? Data(contentsOf: url),
                let content = String(data: data, encoding: .utf8)
            {
                fileContent = content
                print(
                    "FileContentView: Loaded \(content.count) characters from \(url.lastPathComponent)"
                )
            } else {
                fileContent = "(Unable to load file content)"
                print("FileContentView: Failed to load content from \(url.lastPathComponent)")
            }
        } else {
            if FileManager.default.fileExists(atPath: file.path),
                let data = try? Data(contentsOf: url),
                let img = NSImage(data: data)
            {
                image = img
                print("FileContentView: Loaded image from \(url.lastPathComponent)")
            } else {
                print("FileContentView: Failed to load image from \(url.lastPathComponent)")
            }
            fileContent = ""
        }
    }

    func reload() {
        // Change loadID to force .id() to reload the view
        loadID = UUID()
        loadFileContent()
    }
}
