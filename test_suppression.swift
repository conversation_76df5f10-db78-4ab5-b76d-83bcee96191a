#!/usr/bin/env swift

import Foundation

// Simple test to verify suppression logic
class TestSuppression {
    private var suppressedFiles: Set<String> = []
    private let queue = DispatchQueue(label: "test.suppression", attributes: .concurrent)

    func suppressAutoVersioning(for filePath: String) {
        print("🔒 TEST: SUPPRESSING auto-versioning for: \(filePath)")
        queue.async(flags: .barrier) {
            self.suppressedFiles.insert(filePath)
            print(
                "🔒 TEST: Suppression set for: \(filePath) (total suppressed: \(self.suppressedFiles.count))"
            )
        }
    }

    func unsuppressAutoVersioning(for filePath: String) {
        print("🔓 TEST: REMOVING suppression for: \(filePath)")
        queue.async(flags: .barrier) {
            self.suppressedFiles.remove(filePath)
            print(
                "🔓 TEST: Suppression removed for: \(filePath) (total suppressed: \(self.suppressedFiles.count))"
            )
        }
    }

    func isAutoVersioningSuppressed(for filePath: String) -> Bool {
        var suppressed = false
        queue.sync {
            suppressed = self.suppressedFiles.contains(filePath)
        }
        if suppressed {
            print("🔒 TEST: Auto-versioning IS SUPPRESSED for: \(filePath)")
        }
        return suppressed
    }

    func testSuppression() {
        let testFile = "/Users/<USER>/Downloads/test_file.txt"

        print("=== Testing Auto Versioning Suppression ===")

        // Test 1: Check initial state
        print("\n1. Initial state:")
        let initiallySuppressed = isAutoVersioningSuppressed(for: testFile)
        print("   Initially suppressed: \(initiallySuppressed)")

        // Test 2: Set suppression
        print("\n2. Setting suppression:")
        suppressAutoVersioning(for: testFile)

        // Test 3: Check suppression is active
        print("\n3. Checking suppression:")
        let suppressed = isAutoVersioningSuppressed(for: testFile)
        print("   Suppression active: \(suppressed)")

        // Test 4: Simulate restore operation
        print("\n4. Simulating restore operation:")
        print("   - File would be restored here")
        print("   - Auto versioning should be suppressed")

        // Test 5: Remove suppression with delay
        print("\n5. Removing suppression with delay:")
        DispatchQueue.global().asyncAfter(deadline: .now() + 1.0) {
            self.unsuppressAutoVersioning(for: testFile)

            // Test 6: Check suppression is removed
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                print("\n6. Checking suppression after removal:")
                let finalSuppressed = self.isAutoVersioningSuppressed(for: testFile)
                print("   Suppression active: \(finalSuppressed)")
                print("\n=== Test Complete ===")
                exit(0)
            }
        }
    }
}

// Run the test
let test = TestSuppression()
test.testSuppression()

// Keep the script running
RunLoop.main.run()
