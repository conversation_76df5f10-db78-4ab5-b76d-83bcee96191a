import Foundation
import UserNotifications

/// Centralized notification management for Augment application
/// Handles all user notifications including storage warnings, cleanup alerts, and system notifications
public class NotificationManager: NSObject, ObservableObject {

    // MARK: - Singleton
    public static let shared = NotificationManager()

    // MARK: - Dependencies
    private let logger = AugmentLogger.shared
    private let userDefaults = UserDefaults.standard

    // MARK: - Published Properties
    @Published public var notificationsEnabled: Bool = true {
        didSet {
            userDefaults.set(notificationsEnabled, forKey: "notificationsEnabled")
            if notificationsEnabled {
                requestNotificationPermissions()
            }
        }
    }

    @Published public var storageNotificationsEnabled: Bool = true {
        didSet {
            userDefaults.set(storageNotificationsEnabled, forKey: "storageNotificationsEnabled")
        }
    }

    @Published public var cleanupNotificationsEnabled: Bool = true {
        didSet {
            userDefaults.set(cleanupNotificationsEnabled, forKey: "cleanupNotificationsEnabled")
        }
    }

    // MARK: - Notification State
    private var hasPermissions = false
    private var pendingNotifications: [String: UNNotificationRequest] = [:]

    // MARK: - Initialization

    private override init() {
        super.init()
        loadSettings()
        setupNotificationCenter()
        requestNotificationPermissions()
    }

    // MARK: - Settings Management

    private func loadSettings() {
        notificationsEnabled = userDefaults.bool(forKey: "notificationsEnabled")
        storageNotificationsEnabled = userDefaults.bool(forKey: "storageNotificationsEnabled")
        cleanupNotificationsEnabled = userDefaults.bool(forKey: "cleanupNotificationsEnabled")

        // Set defaults if not previously configured
        if userDefaults.object(forKey: "notificationsEnabled") == nil {
            notificationsEnabled = true
        }
        if userDefaults.object(forKey: "storageNotificationsEnabled") == nil {
            storageNotificationsEnabled = true
        }
        if userDefaults.object(forKey: "cleanupNotificationsEnabled") == nil {
            cleanupNotificationsEnabled = true
        }
    }

    private func setupNotificationCenter() {
        UNUserNotificationCenter.current().delegate = self

        // Define notification categories
        let storageWarningCategory = UNNotificationCategory(
            identifier: "STORAGE_WARNING",
            actions: [
                UNNotificationAction(
                    identifier: "OPEN_STORAGE_SETTINGS",
                    title: "Open Storage Settings",
                    options: [.foreground]
                ),
                UNNotificationAction(
                    identifier: "CLEANUP_NOW",
                    title: "Cleanup Now",
                    options: [.foreground]
                ),
            ],
            intentIdentifiers: [],
            options: []
        )

        let storageCriticalCategory = UNNotificationCategory(
            identifier: "STORAGE_CRITICAL",
            actions: [
                UNNotificationAction(
                    identifier: "CLEANUP_NOW",
                    title: "Cleanup Now",
                    options: [.foreground]
                ),
                UNNotificationAction(
                    identifier: "OPEN_STORAGE_SETTINGS",
                    title: "Open Settings",
                    options: [.foreground]
                ),
            ],
            intentIdentifiers: [],
            options: []
        )

        UNUserNotificationCenter.current().setNotificationCategories([
            storageWarningCategory,
            storageCriticalCategory,
        ])
    }

    // MARK: - Permission Management

    private func requestNotificationPermissions() {
        UNUserNotificationCenter.current().requestAuthorization(
            options: [.alert, .sound, .badge]
        ) { [weak self] granted, error in
            DispatchQueue.main.async {
                self?.hasPermissions = granted
                if let error = error {
                    self?.logger.error(
                        "Failed to request notification permissions: \(error)",
                        category: .notifications)
                } else if granted {
                    self?.logger.info("Notification permissions granted", category: .notifications)
                } else {
                    self?.logger.warning(
                        "Notification permissions denied", category: .notifications)
                }
            }
        }
    }

    // MARK: - Storage Notifications

    /// Sends a storage warning notification
    /// - Parameters:
    ///   - spaceName: Name of the affected space
    ///   - percentage: Storage usage percentage
    public func sendStorageWarning(spaceName: String, percentage: Double) {
        guard notificationsEnabled && storageNotificationsEnabled && hasPermissions else {
            logger.debug("Storage warning notification suppressed", category: .notifications)
            return
        }

        let content = UNMutableNotificationContent()
        content.title = "Augment Storage Warning"
        content.body = "Storage for '\(spaceName)' is \(Int(percentage * 100))% full"
        content.sound = .default
        content.categoryIdentifier = "STORAGE_WARNING"
        content.userInfo = [
            "type": "storage_warning",
            "spaceName": spaceName,
            "percentage": percentage,
        ]

        let identifier = "storage_warning_\(spaceName)_\(Date().timeIntervalSince1970)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: nil)

        scheduleNotification(request)
    }

    /// Sends a storage critical notification
    /// - Parameter spaceName: Name of the affected space
    public func sendStorageCritical(spaceName: String) {
        guard notificationsEnabled && storageNotificationsEnabled && hasPermissions else {
            logger.debug("Storage critical notification suppressed", category: .notifications)
            return
        }

        let content = UNMutableNotificationContent()
        content.title = "Augment Storage Critical"
        content.body = "Storage limit exceeded for '\(spaceName)'. Cleanup recommended."
        content.sound = .defaultCritical
        content.categoryIdentifier = "STORAGE_CRITICAL"
        content.userInfo = [
            "type": "storage_critical",
            "spaceName": spaceName,
        ]

        let identifier = "storage_critical_\(spaceName)_\(Date().timeIntervalSince1970)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: nil)

        scheduleNotification(request)
    }

    /// Sends a cleanup completion notification
    /// - Parameters:
    ///   - spaceName: Name of the affected space
    ///   - removedVersions: Number of versions removed
    ///   - freedBytes: Bytes freed
    public func sendCleanupCompletion(spaceName: String, removedVersions: Int, freedBytes: Int64) {
        guard notificationsEnabled && cleanupNotificationsEnabled && hasPermissions else {
            logger.debug("Cleanup completion notification suppressed", category: .notifications)
            return
        }

        let formatter = ByteCountFormatter()
        formatter.countStyle = .file
        let freedString = formatter.string(fromByteCount: freedBytes)

        let content = UNMutableNotificationContent()
        content.title = "Augment Cleanup Complete"
        content.body =
            "Cleaned up \(removedVersions) versions in '\(spaceName)', freed \(freedString)"
        content.sound = .default
        content.userInfo = [
            "type": "cleanup_completion",
            "spaceName": spaceName,
            "removedVersions": removedVersions,
            "freedBytes": freedBytes,
        ]

        let identifier = "cleanup_completion_\(spaceName)_\(Date().timeIntervalSince1970)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: nil)

        scheduleNotification(request)
    }

    /// Sends an error recovery success notification
    /// - Parameter errorId: ID of the recovered error
    public func sendErrorRecoverySuccess(errorId: String) {
        guard notificationsEnabled && hasPermissions else {
            logger.debug("Error recovery notification suppressed", category: .notifications)
            return
        }

        let content = UNMutableNotificationContent()
        content.title = "Error Recovered"
        content.body = "Augment successfully resolved an error automatically"
        content.sound = .default
        content.userInfo = [
            "type": "error_recovery",
            "errorId": errorId,
        ]

        let identifier = "error_recovery_\(errorId)_\(Date().timeIntervalSince1970)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: nil)

        scheduleNotification(request)
    }

    // MARK: - Notification Scheduling

    private func scheduleNotification(_ request: UNNotificationRequest) {
        // Store pending notification
        pendingNotifications[request.identifier] = request

        UNUserNotificationCenter.current().add(request) { [weak self] error in
            if let error = error {
                self?.logger.error(
                    "Failed to schedule notification: \(error)", category: .notifications)
            } else {
                self?.logger.debug(
                    "Scheduled notification: \(request.identifier)", category: .notifications)
            }

            // Remove from pending
            self?.pendingNotifications.removeValue(forKey: request.identifier)
        }
    }

    // MARK: - Notification Management

    /// Cancels all pending notifications
    public func cancelAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        pendingNotifications.removeAll()
        logger.info("Cancelled all pending notifications", category: .notifications)
    }

    /// Cancels notifications for a specific space
    /// - Parameter spaceName: Name of the space
    public func cancelNotifications(for spaceName: String) {
        let identifiersToRemove = pendingNotifications.keys.filter { $0.contains(spaceName) }
        UNUserNotificationCenter.current().removePendingNotificationRequests(
            withIdentifiers: identifiersToRemove)

        for identifier in identifiersToRemove {
            pendingNotifications.removeValue(forKey: identifier)
        }

        logger.info("Cancelled notifications for space: \(spaceName)", category: .notifications)
    }

    /// Gets the current notification authorization status
    public func getAuthorizationStatus(completion: @escaping (UNAuthorizationStatus) -> Void) {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                completion(settings.authorizationStatus)
            }
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension NotificationManager: UNUserNotificationCenterDelegate {

    public func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo

        switch response.actionIdentifier {
        case "OPEN_STORAGE_SETTINGS":
            // Post notification to open storage settings
            NotificationCenter.default.post(
                name: NSNotification.Name("OpenStorageSettings"),
                object: userInfo["spaceName"]
            )

        case "CLEANUP_NOW":
            // Post notification to trigger cleanup
            NotificationCenter.default.post(
                name: NSNotification.Name("TriggerCleanup"),
                object: userInfo["spaceName"]
            )

        default:
            break
        }

        completionHandler()
    }

    public func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) ->
            Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.alert, .sound, .badge])
    }
}
