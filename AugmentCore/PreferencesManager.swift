import Foundation
import SwiftUI
import UserNotifications

/// Centralized preferences management for Augment application
/// Bridges UI preferences with backend functionality and provides persistence
public class PreferencesManager: ObservableObject {

    // MARK: - Dependency Injection (DI) Only
    /// Use dependency injection instead of singleton pattern
    // public static let shared = PreferencesManager() // REMOVED

    // MARK: - Dependencies
    private let userDefaults = UserDefaults.standard
    private let storageManager = StorageManager.shared
    private let notificationManager = NotificationManager.shared
    private let logger = AugmentLogger.shared
    private let configuration: AugmentConfiguration

    // MARK: - Published Properties (UI Bindings)

    // General Preferences
    @Published public var autoVersioningEnabled: Bool = true {
        didSet {
            userDefaults.set(autoVersioningEnabled, forKey: "autoVersioningEnabled")
            applyAutoVersioningSettings()
        }
    }

    // Storage Management Preferences
    @Published public var storageManagementEnabled: Bool = true {
        didSet {
            userDefaults.set(storageManagementEnabled, forKey: "storageManagementEnabled")
            applyStorageManagementSettings()
        }
    }

    @Published public var maxStorageGB: Double = 10.0 {
        didSet {
            userDefaults.set(maxStorageGB, forKey: "maxStorageGB")
            applyStorageManagementSettings()
        }
    }

    @Published public var storageWarningThreshold: Double = 80.0 {
        didSet {
            userDefaults.set(storageWarningThreshold, forKey: "storageWarningThreshold")
            applyStorageManagementSettings()
        }
    }

    @Published public var autoCleanupEnabled: Bool = true {
        didSet {
            userDefaults.set(autoCleanupEnabled, forKey: "autoCleanupEnabled")
            applyCleanupSettings()
        }
    }

    @Published public var cleanupFrequencyHours: Int = 24 {
        didSet {
            userDefaults.set(cleanupFrequencyHours, forKey: "cleanupFrequencyHours")
            applyCleanupSettings()
        }
    }

    @Published public var maxVersionAgeDays: Int = 365 {
        didSet {
            userDefaults.set(maxVersionAgeDays, forKey: "maxVersionAgeDays")
            applyCleanupSettings()
        }
    }

    @Published public var storageNotificationsEnabled: Bool = true {
        didSet {
            userDefaults.set(storageNotificationsEnabled, forKey: "storageNotificationsEnabled")
            applyNotificationSettings()
        }
    }

    // MARK: - Initialization

    public init(configuration: AugmentConfiguration = DependencyContainer.shared.configuration()) {
        self.configuration = configuration
        loadSettings()
        setupSettingsObservers()
        applyAllSettings()
    }

    // MARK: - Settings Management

    /// Loads all settings from UserDefaults
    public func loadSettings() {
        autoVersioningEnabled = userDefaults.bool(forKey: "autoVersioningEnabled")
        storageManagementEnabled = userDefaults.bool(forKey: "storageManagementEnabled")
        maxStorageGB = userDefaults.double(forKey: "maxStorageGB")
        storageWarningThreshold = userDefaults.double(forKey: "storageWarningThreshold")
        autoCleanupEnabled = userDefaults.bool(forKey: "autoCleanupEnabled")
        cleanupFrequencyHours = userDefaults.integer(forKey: "cleanupFrequencyHours")
        maxVersionAgeDays = userDefaults.integer(forKey: "maxVersionAgeDays")
        storageNotificationsEnabled = userDefaults.bool(forKey: "storageNotificationsEnabled")

        // Set defaults if values are not set
        setDefaultsIfNeeded()
    }

    /// Sets default values for settings that haven't been configured
    private func setDefaultsIfNeeded() {
        if maxStorageGB == 0 {
            maxStorageGB = 10.0
        }
        if storageWarningThreshold == 0 {
            storageWarningThreshold = 80.0
        }
        if cleanupFrequencyHours == 0 {
            cleanupFrequencyHours = 24
        }
        if maxVersionAgeDays == 0 {
            maxVersionAgeDays = 365
        }
    }

    /// Applies all settings to backend systems
    private func applyAllSettings() {
        applyAutoVersioningSettings()
        applyStorageManagementSettings()
        applyCleanupSettings()
        applyNotificationSettings()
    }

    // MARK: - Settings Application

    /// Applies auto-versioning settings to the version control system
    private func applyAutoVersioningSettings() {
        // Update configuration
        configuration.storage.autoCleanupEnabledByDefault = autoVersioningEnabled

        // Note: File system access removed during modularization
        // This would need to be injected or accessed differently
        // let fileSystem = DependencyContainer.shared.augmentFileSystem()
        // let spaces = fileSystem.getSpaces()
        // Implementation would apply settings to spaces

        logger.info(
            "Applied auto-versioning settings: enabled=\(autoVersioningEnabled)",
            category: .preferences)
    }

    /// Applies storage management settings to the storage manager
    private func applyStorageManagementSettings() {
        // Update configuration
        configuration.storage.defaultMaxSizeGB = maxStorageGB
        configuration.storage.defaultWarningThreshold = storageWarningThreshold / 100.0

        // Note: StorageManager methods removed during modularization
        // storageManager.setEnabled(storageManagementEnabled)

        if storageManagementEnabled {
            let maxBytes = Int64(maxStorageGB * 1_000_000_000)
            let warningThreshold = storageWarningThreshold / 100.0

            // Note: File system access removed during modularization
            // let fileSystem = DependencyContainer.shared.augmentFileSystem()
            // let spaces = fileSystem.getSpaces()
            // Implementation would apply settings to spaces
        }

        logger.info(
            "Applied storage management settings: enabled=\(storageManagementEnabled), maxGB=\(maxStorageGB), threshold=\(storageWarningThreshold)%",
            category: .preferences)
    }

    /// Applies cleanup settings to the storage manager
    private func applyCleanupSettings() {
        // Update configuration
        configuration.storage.defaultMaxVersionAgeDays = maxVersionAgeDays
        configuration.storage.defaultCleanupFrequencyHours = cleanupFrequencyHours
        configuration.storage.autoCleanupEnabledByDefault = autoCleanupEnabled

        // Note: StorageManager methods removed during modularization
        // if autoCleanupEnabled {
        //     storageManager.startAutomaticCleanup(
        //         frequencyHours: cleanupFrequencyHours,
        //         maxAgeDays: maxVersionAgeDays
        //     )
        // } else {
        //     storageManager.stopAutomaticCleanup()
        // }

        logger.info(
            "Applied cleanup settings: enabled=\(autoCleanupEnabled), frequency=\(cleanupFrequencyHours)h, maxAge=\(maxVersionAgeDays)d",
            category: .preferences)
    }

    /// Applies notification settings
    private func applyNotificationSettings() {
        // Update configuration
        configuration.storage.notificationsEnabledByDefault = storageNotificationsEnabled

        // Apply to notification manager
        notificationManager.storageNotificationsEnabled = storageNotificationsEnabled

        // Note: StorageManager method removed during modularization
        // storageManager.setNotificationsEnabled(storageNotificationsEnabled)

        logger.info(
            "Applied notification settings: enabled=\(storageNotificationsEnabled)",
            category: .preferences)
    }

    /// Sets up observers for settings changes
    private func setupSettingsObservers() {
        // Observe UserDefaults changes for external modifications
        NotificationCenter.default.addObserver(
            forName: UserDefaults.didChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleExternalSettingsChange()
        }
    }

    /// Handles external settings changes (e.g., from other parts of the app)
    private func handleExternalSettingsChange() {
        // Reload settings without triggering didSet observers
        DispatchQueue.main.async { [weak self] in
            self?.loadSettingsWithoutTriggers()
        }
    }

    /// Loads settings without triggering didSet observers
    private func loadSettingsWithoutTriggers() {
        // This method would be used to sync settings from external changes
        // Implementation would temporarily disable observers, load values, then re-enable
    }
}

// MARK: - Public Interface

extension PreferencesManager {

    /// Resets all preferences to default values
    public func resetToDefaults() {
        autoVersioningEnabled = true
        storageManagementEnabled = true
        maxStorageGB = 10.0
        storageWarningThreshold = 80.0
        autoCleanupEnabled = true
        cleanupFrequencyHours = 24
        maxVersionAgeDays = 365
        storageNotificationsEnabled = true

        logger.info("Reset all preferences to defaults", category: .preferences)
    }

    /// Exports current preferences to a dictionary
    public func exportPreferences() -> [String: Any] {
        return [
            "autoVersioningEnabled": autoVersioningEnabled,
            "storageManagementEnabled": storageManagementEnabled,
            "maxStorageGB": maxStorageGB,
            "storageWarningThreshold": storageWarningThreshold,
            "autoCleanupEnabled": autoCleanupEnabled,
            "cleanupFrequencyHours": cleanupFrequencyHours,
            "maxVersionAgeDays": maxVersionAgeDays,
            "storageNotificationsEnabled": storageNotificationsEnabled,
        ]
    }

    /// Imports preferences from a dictionary
    public func importPreferences(_ preferences: [String: Any]) {
        if let value = preferences["autoVersioningEnabled"] as? Bool {
            autoVersioningEnabled = value
        }
        if let value = preferences["storageManagementEnabled"] as? Bool {
            storageManagementEnabled = value
        }
        if let value = preferences["maxStorageGB"] as? Double {
            maxStorageGB = value
        }
        if let value = preferences["storageWarningThreshold"] as? Double {
            storageWarningThreshold = value
        }
        if let value = preferences["autoCleanupEnabled"] as? Bool {
            autoCleanupEnabled = value
        }
        if let value = preferences["cleanupFrequencyHours"] as? Int {
            cleanupFrequencyHours = value
        }
        if let value = preferences["maxVersionAgeDays"] as? Int {
            maxVersionAgeDays = value
        }
        if let value = preferences["storageNotificationsEnabled"] as? Bool {
            storageNotificationsEnabled = value
        }

        logger.info("Imported preferences from external source", category: .preferences)
    }
}
