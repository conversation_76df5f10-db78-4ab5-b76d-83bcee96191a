import Foundation
import SwiftUI

/// Comprehensive error recovery framework for Augment application
/// Handles error detection, recovery strategies, and user guidance
public class ErrorRecoveryManager: ObservableObject {

    // MARK: - Dependency Injection
    private let configuration: AugmentConfiguration
    private let preferencesManager: PreferencesManager
    // MARK: - Singleton (deprecated)
    @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
    public static let shared = ErrorRecoveryManager()

    // MARK: - Published Properties
    @Published public var activeErrors: [RecoverableError] = []
    @Published public var isShowingErrorDialog = false
    @Published public var currentError: RecoverableError?
    @Published public var recoveryInProgress = false

    // MARK: - Dependencies
    private let logger = AugmentLogger.shared
    private let notificationManager = NotificationManager.shared
    private let userDefaults = UserDefaults.standard

    // MARK: - Recovery State
    private var recoveryAttempts: [String: Int] = [:]
    private var errorHistory: [ErrorHistoryEntry] = []
    private let maxRecoveryAttempts = 3

    // MARK: - Initialization

    public init(
        configuration: AugmentConfiguration = DependencyContainer.shared.configuration(),
        preferencesManager: PreferencesManager = DependencyContainer.shared.preferencesManager()
    ) {
        self.configuration = configuration
        self.preferencesManager = preferencesManager
        loadErrorHistory()
        setupErrorMonitoring()
        logger.info(
            "Error recovery manager initialized", category: AugmentLogger.LogCategory.errorRecovery)
    }

    // MARK: - Error Handling

    /// Reports an error and attempts automatic recovery
    /// - Parameters:
    ///   - error: The error that occurred
    ///   - context: Additional context about where the error occurred
    ///   - autoRecover: Whether to attempt automatic recovery
    public func handleError(_ error: Error, context: String? = nil, autoRecover: Bool = true) {
        let recoverableError = createRecoverableError(from: error, context: context)

        // Log the error
        logger.error(
            "Error occurred: \(error.localizedDescription)",
            category: AugmentLogger.LogCategory.errorRecovery)

        // Add to active errors
        DispatchQueue.main.async {
            self.activeErrors.append(recoverableError)
        }

        // Record in history
        recordError(recoverableError)

        // Attempt automatic recovery if enabled
        if autoRecover {
            attemptAutomaticRecovery(for: recoverableError)
        } else {
            // Show error dialog for manual intervention
            DispatchQueue.main.async {
                self.showErrorDialog(for: recoverableError)
            }
        }
    }

    /// Creates a recoverable error from a standard error
    /// - Parameters:
    ///   - error: The original error
    ///   - context: Additional context
    /// - Returns: A recoverable error with recovery strategies
    private func createRecoverableError(from error: Error, context: String?) -> RecoverableError {
        let errorId = UUID().uuidString
        let timestamp = Date()

        // Determine error category and recovery strategies
        let category = categorizeError(error)
        let strategies = getRecoveryStrategies(for: category, error: error)

        return RecoverableError(
            id: errorId,
            originalError: error,
            category: category,
            context: context,
            timestamp: timestamp,
            recoveryStrategies: strategies,
            userMessage: generateUserMessage(for: error, category: category),
            technicalDetails: generateTechnicalDetails(for: error)
        )
    }

    /// Categorizes an error to determine appropriate recovery strategies
    /// - Parameter error: The error to categorize
    /// - Returns: The error category
    private func categorizeError(_ error: Error) -> ErrorCategory {
        // Use string-based categorization instead of type checking
        let errorDescription = error.localizedDescription.lowercased()

        if errorDescription.contains("file") || errorDescription.contains("directory") {
            return .fileSystem
        } else if errorDescription.contains("storage") || errorDescription.contains("disk")
            || errorDescription.contains("space")
        {
            return .storage
        } else if errorDescription.contains("permission") || errorDescription.contains("access") {
            return .permissions
        } else if errorDescription.contains("network") || errorDescription.contains("connection") {
            return .network
        } else if errorDescription.contains("configuration")
            || errorDescription.contains("settings")
        {
            return .configuration
        } else {
            return .unknown
        }
    }

    /// Gets appropriate recovery strategies for an error category
    /// - Parameters:
    ///   - category: The error category
    ///   - error: The original error
    /// - Returns: Array of recovery strategies
    private func getRecoveryStrategies(for category: ErrorCategory, error: Error)
        -> [RecoveryStrategy]
    {
        switch category {
        case .fileSystem:
            return [
                .retryOperation,
                .checkFilePermissions,
                .recreateFile,
                .contactSupport,
            ]
        case .storage:
            return [
                .freeUpSpace,
                .adjustStorageSettings,
                .runCleanup,
                .contactSupport,
            ]
        case .permissions:
            return [
                .requestPermissions,
                .checkSystemSettings,
                .runAsAdministrator,
                .contactSupport,
            ]
        case .network:
            return [
                .retryOperation,
                .checkNetworkConnection,
                .workOffline,
                .contactSupport,
            ]
        case .configuration:
            return [
                .resetConfiguration,
                .restoreDefaults,
                .reimportSettings,
                .contactSupport,
            ]
        case .unknown:
            return [
                .retryOperation,
                .restartApplication,
                .contactSupport,
            ]
        }
    }

    // MARK: - Automatic Recovery

    /// Attempts automatic recovery for an error
    /// - Parameter error: The recoverable error
    private func attemptAutomaticRecovery(for error: RecoverableError) {
        let attemptCount = recoveryAttempts[error.id] ?? 0

        // Check if we've exceeded max attempts
        guard attemptCount < maxRecoveryAttempts else {
            logger.warning(
                "Max recovery attempts exceeded for error: \(error.id)",
                category: AugmentLogger.LogCategory.errorRecovery)
            DispatchQueue.main.async {
                self.showErrorDialog(for: error)
            }
            return
        }

        // Increment attempt count
        recoveryAttempts[error.id] = attemptCount + 1

        // Try the first automatic recovery strategy
        if let strategy = error.recoveryStrategies.first(where: { $0.isAutomatic }) {
            executeRecoveryStrategy(strategy, for: error)
        } else {
            // No automatic strategies available, show dialog
            DispatchQueue.main.async {
                self.showErrorDialog(for: error)
            }
        }
    }

    /// Executes a specific recovery strategy
    /// - Parameters:
    ///   - strategy: The recovery strategy to execute
    ///   - error: The error being recovered from
    private func executeRecoveryStrategy(_ strategy: RecoveryStrategy, for error: RecoverableError)
    {
        logger.info(
            "Executing recovery strategy: \(strategy.rawValue)",
            category: AugmentLogger.LogCategory.errorRecovery)

        switch strategy {
        case .retryOperation:
            // Implement retry logic
            break
        case .freeUpSpace:
            // Implement space cleanup logic
            break
        case .checkFilePermissions:
            // Implement permission check logic
            break
        case .requestPermissions:
            // Implement permission request logic
            break
        case .runCleanup:
            // Implement cleanup logic
            break
        case .resetConfiguration:
            // Implement configuration reset logic
            break
        case .restoreDefaults:
            // Implement defaults restoration logic
            break
        case .checkNetworkConnection:
            // Implement network check logic
            break
        case .recreateFile:
            // Implement file recreation logic
            break
        case .adjustStorageSettings:
            // Implement storage settings adjustment logic
            break
        case .checkSystemSettings:
            // Implement system settings check logic
            break
        case .runAsAdministrator:
            // Implement admin elevation logic
            break
        case .workOffline:
            // Implement offline mode logic
            break
        case .reimportSettings:
            // Implement settings reimport logic
            break
        case .restartApplication:
            // Implement app restart logic
            break
        case .contactSupport:
            // Implement support contact logic
            break
        }
    }

    // MARK: - Recovery Actions

    private func retryFailedOperation(for error: RecoverableError) -> Bool {
        // Implementation would depend on the specific operation that failed
        // For now, simulate a retry with 70% success rate
        return Double.random(in: 0...1) > 0.3
    }

    private func attemptSpaceCleanup() -> Bool {
        let storageManager = StorageManager.shared

        // Try to free up space by running cleanup
        // Note: File system access removed during modularization
        // This would need to be injected or accessed differently
        let spaces: [AugmentSpace] = []  // Placeholder - would get from injected dependency

        var totalFreed: Int64 = 0
        for space in spaces {
            let result = storageManager.cleanupOldVersions(
                olderThan: TimeInterval(30 * 24 * 3600), in: space)  // 30 days
            totalFreed += result.freedBytes
        }

        return totalFreed > 0
    }

    private func checkAndFixFilePermissions(for error: RecoverableError) -> Bool {
        // Check if we can read/write to the file or directory
        guard let context = error.context,
            let url = URL(string: context)
        else {
            return false
        }

        let fileManager = FileManager.default
        return fileManager.isReadableFile(atPath: url.path)
            && fileManager.isWritableFile(atPath: url.path)
    }

    private func requestSystemPermissions() -> Bool {
        // This would typically show a system dialog or guide the user
        // For now, we'll return false to indicate manual intervention needed
        return false
    }

    private func runAutomaticCleanup() -> Bool {
        // Placeholder for automatic cleanup
        // storageManager.startAutomaticCleanup(frequencyHours: 1, maxAgeDays: 30)
        return true
    }

    private func resetToDefaultConfiguration() -> Bool {
        configuration.resetToDefaults()
        return true
    }

    private func restoreDefaultSettings() -> Bool {
        preferencesManager.resetToDefaults()
        return true
    }

    private func checkNetworkConnectivity() -> Bool {
        // Simple network check - in a real implementation, this would be more sophisticated
        let url = URL(string: "https://www.apple.com")!
        let semaphore = DispatchSemaphore(value: 0)
        var isConnected = false

        let task = URLSession.shared.dataTask(with: url) { _, _, error in
            isConnected = error == nil
            semaphore.signal()
        }
        task.resume()

        _ = semaphore.wait(timeout: .now() + 5.0)
        return isConnected
    }

    // MARK: - Error Management

    /// Marks an error as recovered and removes it from active errors
    /// - Parameter error: The recovered error
    private func markErrorAsRecovered(_ error: RecoverableError) {
        activeErrors.removeAll { $0.id == error.id }
        recoveryAttempts.removeValue(forKey: error.id)

        // Update error history
        if let index = errorHistory.firstIndex(where: { $0.error.id == error.id }) {
            errorHistory[index].isResolved = true
            errorHistory[index].resolvedAt = Date()
        }

        // Send success notification
        notificationManager.sendErrorRecoverySuccess(errorId: error.id)
    }

    /// Handles recovery failure by trying next strategy or showing dialog
    /// - Parameter error: The error that failed to recover
    private func handleRecoveryFailure(for error: RecoverableError) {
        let attemptCount = recoveryAttempts[error.id] ?? 0

        if attemptCount < maxRecoveryAttempts {
            // Try next automatic strategy
            let usedStrategies = Array(error.recoveryStrategies.prefix(attemptCount))
            if let nextStrategy = error.recoveryStrategies.first(where: { strategy in
                !usedStrategies.contains(strategy) && strategy.isAutomatic
            }) {
                executeRecoveryStrategy(nextStrategy, for: error)
                return
            }
        }

        // No more automatic strategies or max attempts reached
        showErrorDialog(for: error)
    }

    /// Shows error dialog for manual intervention
    /// - Parameter error: The error to show
    private func showErrorDialog(for error: RecoverableError) {
        currentError = error
        isShowingErrorDialog = true
    }

    // MARK: - Error History

    private func recordError(_ error: RecoverableError) {
        let historyEntry = ErrorHistoryEntry(
            error: error,
            occurredAt: Date(),
            isResolved: false,
            resolvedAt: nil
        )

        errorHistory.append(historyEntry)

        // Keep only last 100 errors
        if errorHistory.count > 100 {
            errorHistory.removeFirst(errorHistory.count - 100)
        }

        saveErrorHistory()
    }

    private func loadErrorHistory() {
        // Load error history from UserDefaults
        if let data = userDefaults.data(forKey: "ErrorHistory"),
            let history = try? JSONDecoder().decode([ErrorHistoryEntry].self, from: data)
        {
            errorHistory = history
        }
    }

    private func saveErrorHistory() {
        // Save to UserDefaults or file
        // Implementation would serialize error history
    }

    private func setupErrorMonitoring() {
        // Setup error monitoring - placeholder for now
        // let fileSystem = DependencyContainer.shared.augmentFileSystem()
        // Implementation would go here
    }

    // MARK: - Public Interface

    /// Gets error history for display in UI
    /// - Returns: Array of error history entries
    public func getErrorHistory() -> [ErrorHistoryEntry] {
        return errorHistory.sorted { $0.occurredAt > $1.occurredAt }
    }

    /// Dismisses the current error dialog
    public func dismissErrorDialog() {
        isShowingErrorDialog = false
        currentError = nil
    }

    /// Manually triggers a recovery strategy
    /// - Parameters:
    ///   - strategy: The strategy to execute
    ///   - error: The error to recover from
    public func executeManualRecovery(_ strategy: RecoveryStrategy, for error: RecoverableError) {
        executeRecoveryStrategy(strategy, for: error)
    }

    /// Clears all resolved errors from active list
    public func clearResolvedErrors() {
        activeErrors.removeAll { error in
            errorHistory.contains { $0.error.id == error.id && $0.isResolved }
        }
    }

    // MARK: - Message Generation

    /// Generates user-friendly message for an error
    /// - Parameters:
    ///   - error: The original error
    ///   - category: The error category
    /// - Returns: User-friendly error message
    private func generateUserMessage(for error: Error, category: ErrorCategory) -> String {
        switch category {
        case .fileSystem:
            return
                "There was a problem accessing a file. This might be due to the file being moved, deleted, or locked by another application."
        case .storage:
            return
                "Your storage space is running low or has reached its limit. Consider freeing up space or adjusting your storage settings."
        case .permissions:
            return
                "Augment doesn't have the necessary permissions to access this file or folder. You may need to grant additional permissions."
        case .network:
            return
                "There was a network connectivity issue. Please check your internet connection and try again."
        case .configuration:
            return
                "There's an issue with your Augment configuration. Resetting to default settings may resolve this problem."
        case .unknown:
            return
                "An unexpected error occurred. Augment will attempt to recover automatically, but you may need to restart the application."
        }
    }

    /// Generates technical details for an error
    /// - Parameter error: The original error
    /// - Returns: Technical error details
    private func generateTechnicalDetails(for error: Error) -> String {
        var details = "Error: \(error.localizedDescription)\n"
        details += "Type: \(type(of: error))\n"

        if let nsError = error as NSError? {
            details += "Domain: \(nsError.domain)\n"
            details += "Code: \(nsError.code)\n"

            if !nsError.userInfo.isEmpty {
                details += "User Info: \(nsError.userInfo)\n"
            }
        }

        details += "Timestamp: \(ISO8601DateFormatter().string(from: Date()))\n"

        return details
    }

    private func someMethod() {
        // Use the injected preferencesManager property
        // ...
    }
}

// MARK: - Data Models

/// Represents an error that can be recovered from
public struct RecoverableError: Identifiable, Equatable, Codable {
    public let id: String
    public let originalError: Error
    public let category: ErrorCategory
    public let context: String?
    public let timestamp: Date
    public let recoveryStrategies: [RecoveryStrategy]
    public let userMessage: String
    public let technicalDetails: String

    public static func == (lhs: RecoverableError, rhs: RecoverableError) -> Bool {
        return lhs.id == rhs.id
    }

    // Custom coding keys to handle Error type
    private enum CodingKeys: String, CodingKey {
        case id, category, context, timestamp, recoveryStrategies, userMessage, technicalDetails
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        category = try container.decode(ErrorCategory.self, forKey: .category)
        context = try container.decodeIfPresent(String.self, forKey: .context)
        timestamp = try container.decode(Date.self, forKey: .timestamp)
        recoveryStrategies = try container.decode(
            [RecoveryStrategy].self, forKey: .recoveryStrategies)
        userMessage = try container.decode(String.self, forKey: .userMessage)
        technicalDetails = try container.decode(String.self, forKey: .technicalDetails)

        // Create a placeholder error since Error is not Codable
        originalError = NSError(
            domain: "AugmentError", code: 0,
            userInfo: [
                NSLocalizedDescriptionKey: userMessage
            ])
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(category, forKey: .category)
        try container.encodeIfPresent(context, forKey: .context)
        try container.encode(timestamp, forKey: .timestamp)
        try container.encode(recoveryStrategies, forKey: .recoveryStrategies)
        try container.encode(userMessage, forKey: .userMessage)
        try container.encode(technicalDetails, forKey: .technicalDetails)
    }

    public init(
        id: String, originalError: Error, category: ErrorCategory, context: String?,
        timestamp: Date, recoveryStrategies: [RecoveryStrategy], userMessage: String,
        technicalDetails: String
    ) {
        self.id = id
        self.originalError = originalError
        self.category = category
        self.context = context
        self.timestamp = timestamp
        self.recoveryStrategies = recoveryStrategies
        self.userMessage = userMessage
        self.technicalDetails = technicalDetails
    }
}

/// Error categories for determining recovery strategies
public enum ErrorCategory: String, CaseIterable, Codable {
    case fileSystem = "File System"
    case storage = "Storage"
    case permissions = "Permissions"
    case network = "Network"
    case configuration = "Configuration"
    case unknown = "Unknown"

    var icon: String {
        switch self {
        case .fileSystem:
            return "doc.badge.exclamationmark"
        case .storage:
            return "externaldrive.badge.exclamationmark"
        case .permissions:
            return "lock.badge.exclamationmark"
        case .network:
            return "wifi.exclamationmark"
        case .configuration:
            return "gearshape.badge.exclamationmark"
        case .unknown:
            return "exclamationmark.triangle"
        }
    }
}

/// Available recovery strategies
public enum RecoveryStrategy: String, CaseIterable, Codable {
    case retryOperation = "Retry Operation"
    case freeUpSpace = "Free Up Space"
    case checkFilePermissions = "Check File Permissions"
    case requestPermissions = "Request Permissions"
    case runCleanup = "Run Cleanup"
    case resetConfiguration = "Reset Configuration"
    case restoreDefaults = "Restore Defaults"
    case checkNetworkConnection = "Check Network"
    case recreateFile = "Recreate File"
    case adjustStorageSettings = "Adjust Storage Settings"
    case checkSystemSettings = "Check System Settings"
    case runAsAdministrator = "Run as Administrator"
    case workOffline = "Work Offline"
    case reimportSettings = "Reimport Settings"
    case restartApplication = "Restart Application"
    case contactSupport = "Contact Support"

    /// Whether this strategy can be executed automatically
    var isAutomatic: Bool {
        switch self {
        case .retryOperation, .freeUpSpace, .runCleanup, .resetConfiguration, .restoreDefaults,
            .checkNetworkConnection:
            return true
        default:
            return false
        }
    }

    var icon: String {
        switch self {
        case .retryOperation:
            return "arrow.clockwise"
        case .freeUpSpace:
            return "trash"
        case .checkFilePermissions:
            return "checkmark.shield"
        case .requestPermissions:
            return "key"
        case .runCleanup:
            return "sparkles"
        case .resetConfiguration:
            return "gearshape.arrow.triangle.2.circlepath"
        case .restoreDefaults:
            return "arrow.uturn.backward"
        case .checkNetworkConnection:
            return "wifi"
        case .recreateFile:
            return "doc.badge.plus"
        case .adjustStorageSettings:
            return "slider.horizontal.3"
        case .checkSystemSettings:
            return "gearshape"
        case .runAsAdministrator:
            return "person.badge.key"
        case .workOffline:
            return "wifi.slash"
        case .reimportSettings:
            return "square.and.arrow.down"
        case .restartApplication:
            return "power"
        case .contactSupport:
            return "questionmark.circle"
        }
    }
}

/// Error history entry for tracking and analysis
public struct ErrorHistoryEntry: Codable {
    public let error: RecoverableError
    public let occurredAt: Date
    public var isResolved: Bool
    public var resolvedAt: Date?
}
