{"name": "augment-website", "version": "1.0.0", "description": "Official website for Augment - Intelligent File Versioning for macOS", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-syntax-highlighter": "^15.5.0", "autoprefixer": "^10.4.0", "clsx": "^2.0.0", "framer-motion": "^10.16.0", "fuse.js": "^7.0.0", "gray-matter": "^4.0.3", "lucide-react": "^0.292.0", "next": "^15.3.3", "next-themes": "^0.2.1", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-syntax-highlighter": "^15.5.0", "remark": "^15.0.0", "remark-gfm": "^4.0.0", "remark-html": "^16.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}, "keywords": ["file versioning", "macOS", "backup", "version control", "automatic versioning", "file management"], "author": "Augment Team", "license": "MIT"}