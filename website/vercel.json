{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "functions": {"app/api/download/route.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/downloads/(.*)", "destination": "/api/download?file=$1"}]}