Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project Augment.xcodeproj -scheme Augment -configuration Debug clean build

ComputePackagePrebuildTargetDependencyGraph

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

** CLEAN SUCCEEDED **

ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (1 target)
    Target 'Augment' in project 'Augment' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

Build description signature: dccce8608a943ddec870079d389228a6
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/XCBuildData/dccce8608a943ddec870079d389228a6.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache
    cd /Users/<USER>/Downloads/AugmentApp\ 2/Augment.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache

CreateBuildDirectory /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products
    cd /Users/<USER>/Downloads/AugmentApp\ 2/Augment.xcodeproj
    builtin-create-build-directory /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products

CreateBuildDirectory /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex
    cd /Users/<USER>/Downloads/AugmentApp\ 2/Augment.xcodeproj
    builtin-create-build-directory /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex

CreateBuildDirectory /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug
    cd /Users/<USER>/Downloads/AugmentApp\ 2/Augment.xcodeproj
    builtin-create-build-directory /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug

CreateBuildDirectory /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/EagerLinkingTBDs/Debug
    cd /Users/<USER>/Downloads/AugmentApp\ 2/Augment.xcodeproj
    builtin-create-build-directory /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/EagerLinkingTBDs/Debug

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml
    cd /Users/<USER>/Downloads/AugmentApp\ 2/Augment.xcodeproj
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.hmap (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.hmap

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-non-framework-target-headers.hmap (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-non-framework-target-headers.hmap

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt

ValidateDevelopmentAssets /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    builtin-validate-development-assets --validate YES_ERROR /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Preview\ Content

MkDir /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents

MkDir /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/Resources (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/Resources

MkDir /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app

MkDir /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/MacOS (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/MacOS

ProcessProductPackaging /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Augment.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    
    Entitlements:
    
    {
    "com.apple.security.app-sandbox" = 1;
    "com.apple.security.files.bookmarks.app-scope" = 1;
    "com.apple.security.files.downloads.read-write" = 1;
    "com.apple.security.files.user-selected.read-write" = 1;
    "com.apple.security.get-task-allow" = 1;
    "com.apple.security.network.client" = 1;
    "com.apple.security.temporary-exception.files.absolute-path.read-write" =     (
        "/"
    );
}
    
    builtin-productPackagingUtility /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Augment.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der --raw

Ld /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib normal (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target arm64-apple-macos13.0 -dynamiclib -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -O0 -L/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -F/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -install_name @rpath/Augment.debug.dylib -dead_strip -rdynamic -Xlinker -no_deduplicate -Xlinker -dependency_info -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat -Xlinker -no_adhoc_codesign -o /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib

MkDir /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned

MkDir /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned

GenerateAssetSymbols /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Preview\ Content/Preview\ Assets.xcassets /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Assets.xcassets (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /Applications/Xcode.app/Contents/Developer/usr/bin/actool /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Preview\ Content/Preview\ Assets.xcassets /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Assets.xcassets --compile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/Resources --output-format human-readable-text --notices --warnings --export-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies --output-partial-info-plist /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist --app-icon AppIcon --accent-color AccentColor --enable-on-demand-resources NO --development-region en --target-device mac --minimum-deployment-target 13.0 --platform macosx --bundle-identifier com.augment.Augment --generate-swift-asset-symbols /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift --generate-objc-asset-symbols /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h --generate-asset-symbol-index /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist
/* com.apple.actool.compilation-results */
/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist
/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h
/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift


CompileAssetCatalogVariant thinned /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/Resources /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Preview\ Content/Preview\ Assets.xcassets /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Assets.xcassets (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    /Applications/Xcode.app/Contents/Developer/usr/bin/actool /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Preview\ Content/Preview\ Assets.xcassets /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Assets.xcassets --compile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned --output-format human-readable-text --notices --warnings --export-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_thinned --output-partial-info-plist /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_thinned --app-icon AppIcon --accent-color AccentColor --enable-on-demand-resources NO --development-region en --target-device mac --minimum-deployment-target 13.0 --platform macosx
/* com.apple.actool.compilation-results */
/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_thinned


LinkAssetCatalog /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Preview\ Content/Preview\ Assets.xcassets /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Assets.xcassets (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    builtin-linkAssetCatalog --thinned /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned --thinned-dependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_thinned --thinned-info-plist-content /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_thinned --unthinned /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned --unthinned-dependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_unthinned --unthinned-info-plist-content /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_unthinned --output /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/Resources --plist-output /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/Info.plist /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Info.plist (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    builtin-infoPlistUtility /Users/<USER>/Downloads/AugmentApp\ 2/Augment/Info.plist -producttype com.apple.product-type.application -genpkginfo /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/PkgInfo -expandbuildsettings -platform macosx -additionalcontentfile /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist -o /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/Augment.app/Contents/Info.plist

SwiftDriver Augment normal arm64 com.apple.xcode.tools.swift.compiler (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name Augment -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList -DDEBUG -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -target arm64-apple-macos13.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -F /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64 -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h -working-directory /Users/<USER>/Downloads/AugmentApp\ 2 -experimental-emit-module-separately -disable-cmo

SwiftCompile normal arm64 Compiling\ ContentView.swift,\ AugmentApp.swift,\ SpaceDetailView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ContentView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/AugmentApp.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SpaceDetailView.swift (in target 'Augment' from project 'Augment')

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ContentView.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/Augment/AugmentApp.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SpaceDetailView.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftEmitModule normal arm64 Emitting\ module\ for\ Augment (in target 'Augment' from project 'Augment')
Failed frontend command:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -emit-module -experimental-skip-non-inlinable-function-bodies-without-types /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ContentView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/AugmentApp.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SpaceDetailView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SearchView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ConflictResolutionView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/VersionBrowser.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/BackupManager.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/PreviewEngine.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/FileOperationInterceptor.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/AugmentFileSystem.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/VersionControl.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileType.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/AugmentSpace.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileItem.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SearchEngine.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/AugmentFUSE.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileSystemMonitor.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/ConflictResolution.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/NetworkSync.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SnapshotManager.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift -target arm64-apple-macos13.0 -Xllvm -aarch64-use-tbi -enable-objc-interop -stack-check -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -F /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=4 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Downloads/AugmentApp\ 2 -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Downloads/AugmentApp\ 2 -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources -Xcc -DDEBUG\=1 -module-name Augment -frontend-parseable-output -disable-clang-spi -target-sdk-version 15.5 -target-sdk-name macosx15.5 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -emit-module-doc-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc -emit-module-source-info-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-master-emit-module.dia -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-master-emit-module.d -o /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule -emit-abi-descriptor-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json

EmitSwiftModule normal arm64 (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    
/Users/<USER>/Downloads/AugmentApp 2/Augment/ContentView.swift:1:8: error: no such module 'AugmentCore'
import AugmentCore
       ^

SwiftCompile normal arm64 Compiling\ SearchView.swift,\ ConflictResolutionView.swift,\ VersionBrowser.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SearchView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ConflictResolutionView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/VersionBrowser.swift (in target 'Augment' from project 'Augment')

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SearchView.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ConflictResolutionView.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/Augment/VersionBrowser.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 Compiling\ ConflictResolution.swift,\ NetworkSync.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/ConflictResolution.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/NetworkSync.swift (in target 'Augment' from project 'Augment')
Failed frontend command:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ContentView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/AugmentApp.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SpaceDetailView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SearchView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ConflictResolutionView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/VersionBrowser.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/BackupManager.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/PreviewEngine.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/FileOperationInterceptor.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/AugmentFileSystem.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/VersionControl.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileType.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/AugmentSpace.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileItem.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SearchEngine.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/AugmentFUSE.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileSystemMonitor.swift -primary-file /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/ConflictResolution.swift -primary-file /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/NetworkSync.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SnapshotManager.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.dia -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64 -target arm64-apple-macos13.0 -Xllvm -aarch64-use-tbi -enable-objc-interop -stack-check -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -F /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=4 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Downloads/AugmentApp\ 2 -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Downloads/AugmentApp\ 2 -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources -Xcc -DDEBUG\=1 -module-name Augment -frontend-parseable-output -disable-clang-spi -target-sdk-version 15.5 -target-sdk-name macosx15.5 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o -o /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o -index-unit-output-path /Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o -index-unit-output-path /Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Index.noindex/DataStore -index-system-modules

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/ConflictResolution.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    
/Users/<USER>/Downloads/AugmentApp 2/Augment/ContentView.swift:1:8: error: no such module 'AugmentCore'
import AugmentCore
       ^

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/NetworkSync.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    
/Users/<USER>/Downloads/AugmentApp 2/Augment/ContentView.swift:1:8: error: no such module 'AugmentCore'
import AugmentCore
       ^

SwiftCompile normal arm64 Compiling\ AugmentSpace.swift,\ FileItem.swift,\ SearchEngine.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/AugmentSpace.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileItem.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SearchEngine.swift (in target 'Augment' from project 'Augment')

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/AugmentSpace.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileItem.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SearchEngine.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 Compiling\ BackupManager.swift,\ PreviewEngine.swift,\ FileOperationInterceptor.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/BackupManager.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/PreviewEngine.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/FileOperationInterceptor.swift (in target 'Augment' from project 'Augment')

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/BackupManager.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/PreviewEngine.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/FileOperationInterceptor.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 Compiling\ SnapshotManager.swift,\ GeneratedAssetSymbols.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SnapshotManager.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift (in target 'Augment' from project 'Augment')
Failed frontend command:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ContentView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/AugmentApp.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SpaceDetailView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/SearchView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/ConflictResolutionView.swift /Users/<USER>/Downloads/AugmentApp\ 2/Augment/VersionBrowser.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/BackupManager.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/PreviewEngine.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/FileOperationInterceptor.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/AugmentFileSystem.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/VersionControl.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileType.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/AugmentSpace.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileItem.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SearchEngine.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/AugmentFUSE.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileSystemMonitor.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/ConflictResolution.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/NetworkSync.swift -primary-file /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SnapshotManager.swift -primary-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.dia -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64 -target arm64-apple-macos13.0 -Xllvm -aarch64-use-tbi -enable-objc-interop -stack-check -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -F /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=4 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Downloads/AugmentApp\ 2 -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Downloads/AugmentApp\ 2 -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources -Xcc -DDEBUG\=1 -module-name Augment -frontend-parseable-output -disable-clang-spi -target-sdk-version 15.5 -target-sdk-name macosx15.5 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o -o /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o -index-unit-output-path /Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o -index-unit-output-path /Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Index.noindex/DataStore -index-system-modules

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/SnapshotManager.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    
/Users/<USER>/Downloads/AugmentApp 2/Augment/ContentView.swift:1:8: error: no such module 'AugmentCore'
import AugmentCore
       ^

SwiftCompile normal arm64 /Users/<USER>/Library/Developer/Xcode/DerivedData/Augment-asmtthghazdmzzfnvzhwjwikjxmw/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    
/Users/<USER>/Downloads/AugmentApp 2/Augment/ContentView.swift:1:8: error: no such module 'AugmentCore'
import AugmentCore
       ^

SwiftCompile normal arm64 Compiling\ AugmentFileSystem.swift,\ VersionControl.swift,\ FileType.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/AugmentFileSystem.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/VersionControl.swift /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileType.swift (in target 'Augment' from project 'Augment')

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentFileSystem/AugmentFileSystem.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/VersionControl.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

SwiftCompile normal arm64 /Users/<USER>/Downloads/AugmentApp\ 2/AugmentCore/FileType.swift (in target 'Augment' from project 'Augment')
    cd /Users/<USER>/Downloads/AugmentApp\ 2
    

